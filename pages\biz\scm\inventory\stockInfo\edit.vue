<template>
	<view class="edit-container">
		<!-- 表单内容 -->
		<scroll-view scroll-y class="form-content" v-if="formData">
			<uv-form
				ref="formRef"
				:model="formData"
				:rules="formRules"
				label-position="top"
				label-width="120"
			>
				<!-- 物料信息 -->
				<uv-form-item label="物料名称">
					<uv-input
						v-model="formData.materialName"
						placeholder="物料名称"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料编码">
					<uv-input
						v-model="formData.materialCode"
						placeholder="物料编码"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料规格">
					<uv-input
						v-model="formData.spec"
						placeholder="物料规格"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料类型">
					<uv-input
						:value="getMaterialTypeText(formData.materialType)"
						placeholder="物料类型"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="物料来源">
					<uv-input
						:value="getMaterialSourceText(formData.materialSource)"
						placeholder="物料来源"
						disabled
					/>
				</uv-form-item>

				<!-- 库存信息 -->
				<uv-form-item label="库存数量">
					<uv-input
						:value="formatQuantity(formData.quantity) + ' ' + getUnitName(formData.quantityUnit)"
						placeholder="库存数量"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="锁定数量">
					<uv-input
						:value="formatQuantity(formData.lockQuantity) + ' ' + getUnitName(formData.quantityUnit)"
						placeholder="锁定数量"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="未锁数量">
					<uv-input
						:value="formatQuantity(formData.unlockQuantity) + ' ' + getUnitName(formData.quantityUnit)"
						placeholder="可用数量"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="数量单位">
					<uv-input
						:value="getUnitName(formData.quantityUnit)"
						placeholder="数量单位"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="基本单位">
					<uv-input
						:value="getUnitName(formData.auxiliaryUnit)"
						placeholder="基本单位"
						disabled
					/>
				</uv-form-item>

				<uv-form-item label="状态">
					<uv-input
						:value="getStatusText(formData.status)"
						placeholder="状态"
						disabled
					/>
				</uv-form-item>

				<!-- 可编辑字段 -->
				<uv-form-item label="基本单位数量" prop="auxiliaryQuantity">
					<uv-input
						v-model="formData.auxiliaryQuantity"
						type="number"
						placeholder="请输入基本单位数量"
					/>
				</uv-form-item>

				<uv-form-item label="库存数量" prop="inventoryQuantity">
					<uv-input
						v-model="formData.inventoryQuantity"
						type="number"
						placeholder="请输入库存数量"
					/>
				</uv-form-item>

				<uv-form-item label="价格" prop="price">
					<uv-input
						v-model="formData.price"
						type="number"
						placeholder="请输入价格"
					/>
				</uv-form-item>

				<uv-form-item label="价格单位" prop="priceUnit">
					<SelectPicker
						v-model="formData.priceUnit"
						:options="unitOptions || []"
						title="选择价格单位"
						label-field="name"
						value-field="id"
						placeholder="请选择价格单位"
					/>
				</uv-form-item>

				<uv-form-item label="总价值" prop="totalCost">
					<uv-input
						v-model="formData.totalCost"
						type="number"
						placeholder="请输入总价值"
					/>
				</uv-form-item>

				<uv-form-item label="采购价格" prop="purchasePrice">
					<uv-input
						v-model="formData.purchasePrice"
						type="number"
						placeholder="请输入采购价格"
					/>
				</uv-form-item>

				<uv-form-item label="销售价格" prop="salePrice">
					<uv-input
						v-model="formData.salePrice"
						type="number"
						placeholder="请输入销售价格"
					/>
				</uv-form-item>

				<uv-form-item label="入库日期" prop="inDate">
					<uni-datetime-picker
						v-model="formData.inDate"
						type="date"
						placeholder="请选择入库日期"
						:clear-icon="false"
					/>
				</uv-form-item>

				<uv-form-item label="备注" prop="remark">
					<uv-input
						v-model="formData.remark"
						type="textarea"
						placeholder="请输入备注"
						maxlength="200"
						:autoHeight="true"
					/>
				</uv-form-item>

				<!-- 仓库信息 -->
				<uv-form-item label="仓库名称" prop="warehouseId">
					<uni-data-picker
						v-model="formData.warehouseId"
						:localdata="warehouseTreeData || []"
						popup-title="选择仓库"
						placeholder="请选择仓库"
						:map="{text: 'name', value: 'id'}"
						@change="handleWarehouseChange"
					/>
				</uv-form-item>

				<uv-form-item label="仓位名称" prop="locationId">
					<SelectPicker
						v-model="formData.locationId"
						:options="locationOptions || []"
						title="选择仓位"
						label-field="name"
						value-field="id"
						placeholder="请选择仓位"
					/>
				</uv-form-item>

				<!-- 多仓位表单项预留区域 -->
				<!--
				TODO: 多仓位功能预留
				当需要支持多仓位库存管理时，可以在此处添加：
				1. 多仓位库存分布表格
				2. 仓位间库存调拨功能
				3. 分仓位库存统计
				-->

			</uv-form>

			<!-- 库存交易明细 -->
			<view class="transaction-section">
				<view class="transaction-title">库存交易明细</view>
				<view v-if="transactionList && transactionList.length > 0" class="transaction-list">
					<view v-for="(transaction, index) in transactionList" :key="index" class="transaction-item" v-if="transaction">
						<view class="transaction-header">
							<text class="transaction-type">{{ getTransactionTypeText(transaction.transactionType) }} - {{ getTransactionDirectionText(transaction.transactionDirection) }}</text>
							<text class="transaction-date">{{ formatDate(transaction.moveDate || transaction.createTime) }}</text>
						</view>
						<view class="transaction-details">
							<text class="transaction-no">单号：{{ transaction.bizNo || transaction.documentNo || '-' }}</text>
							<text class="transaction-batch">批号：{{ transaction.inventoryBatchNo || '-' }}</text>
						</view>
						<view class="transaction-quantities">
							<text class="transaction-quantity">
								数量：{{ formatQuantity(transaction.quantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
							<text v-if="transaction.lockQuantity" class="lock-quantity">
								锁定：{{ formatQuantity(transaction.lockQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
						</view>
						<view class="transaction-before-after">
							<text class="before-quantity">
								出入库前：{{ formatQuantity(transaction.beforeQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
							<text class="after-quantity">
								出入库后：{{ formatQuantity(transaction.afterQuantity) }} {{ getUnitName(transaction.quantityUnit) }}
							</text>
						</view>
						<view class="transaction-warehouse">
							<text class="from-warehouse">
								来源仓库：{{ transaction.fromWarehouseName || '-' }}
							</text>
							<text class="to-warehouse">
								目标仓库：{{ transaction.toWarehouseName || '-' }}
							</text>
						</view>
						<view v-if="transaction.remark" class="transaction-remark">
							<text class="remark-text">备注：{{ transaction.remark }}</text>
						</view>
					</view>
				</view>
				<view v-else class="no-data">
					<text>暂无交易明细</text>
				</view>
			</view>
		</scroll-view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions" v-if="formData">
			<uv-button
				type="primary"
				size="large"
				:loading="saving || false"
				@click="handleSave"
			>
				保存
			</uv-button>
		</view>

		<!-- 空数据状态 -->
		<view v-if="!loading && !formData && stockId" class="empty-container">
			<view class="empty-content">
				<text class="empty-text">暂无库存信息</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
import { stockInfoApi } from '@/api/scm/inventory/stockInfo/index.js'
import { getBatchDictOptions, DICT_TYPE } from '@/utils/dict.js'
import { getWarehouseListApi } from '@/api/scm/base/warehouse/index.js'
import { getWarehouseLocationPageApi } from '@/api/scm/inventory/warehouseLocation/index.js'
import SelectPicker from '@/components/SelectPicker/SelectPicker.vue'
import { handleTree } from '../../../../../utils/tree'

export default {
	components: {
		SelectPicker
	},
	data() {
		return {
			stockId: null,
			loading: false,
			saving: false,
			formData: null,
			transactionList: [],
			// 字典数据
			materialTypeOptions: [],
			materialSourceOptions: [],
			statusOptions: [],
			unitOptions: [],
			dictData: {},
			// 仓库和仓位数据
			warehouseTreeData: [],
			locationOptions: [],
			// 表单验证规则
			formRules: {
				auxiliaryQuantity: [
					{
						type: 'number',
						message: '请输入有效的数字',
						trigger: ['blur', 'change']
					}
				],
				inventoryQuantity: [
					{
						type: 'number',
						message: '请输入有效的数字',
						trigger: ['blur', 'change']
					}
				],
				price: [
					{
						type: 'number',
						message: '请输入有效的价格',
						trigger: ['blur', 'change']
					}
				],
				totalCost: [
					{
						type: 'number',
						message: '请输入有效的总价值',
						trigger: ['blur', 'change']
					}
				],
				purchasePrice: [
					{
						type: 'number',
						message: '请输入有效的采购价格',
						trigger: ['blur', 'change']
					}
				],
				salePrice: [
					{
						type: 'number',
						message: '请输入有效的销售价格',
						trigger: ['blur', 'change']
					}
				],
				remark: [
					{
						max: 200,
						message: '备注不能超过200个字符',
						trigger: ['blur', 'change']
					}
				]
			}
		}
	},
	async onLoad() {
		const eventChannel = this.getOpenerEventChannel()
		if(eventChannel){
			eventChannel.on('acceptDataFormOpener',(data) => {
				if(data) {
					this.stockId = data.stockId
					// 获取到stockId后立即初始化数据
					this.initData()
				}
			})
		}
	},
	methods: {

		// 初始化数据
		async initData() {
			await this.getDictData()
			await this.loadWarehouses()
			this.loadData()
		},

		// 获取字典数据
		async getDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.MATERIAL_TYPE,
					DICT_TYPE.MATERIAL_SOURCE,
					DICT_TYPE.STOCK_STATUS,
					DICT_TYPE.SCM_BIZ_TYPE,
					DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION
				]
				this.dictData = await getBatchDictOptions(dictTypes)

				// 设置选项数据
				this.materialTypeOptions = this.dictData[DICT_TYPE.MATERIAL_TYPE] || []
				this.materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
				this.statusOptions = this.dictData[DICT_TYPE.STOCK_STATUS] || []

				// 加载单位数据
				await this.loadUnits()

			} catch (error) {
				console.error('获取字典数据失败:', error)
				// 字典数据加载失败不影响主要功能，只是显示可能不够友好
				// 设置默认空数组避免后续处理出错
				this.dictData = {}
				this.materialTypeOptions = []
				this.materialSourceOptions = []
				this.statusOptions = []
				this.unitOptions = []
			}
		},

		// 加载单位数据
		async loadUnits() {
			try {
				const response = await stockInfoApi.getUnitList({ pageNo: 1, pageSize: 100 })
				let units = []
				if (response && response.data && response.data.list) {
					units = response.data.list
				}

				this.unitOptions = units
			} catch (error) {
				this.unitOptions = []
			}
		},

		// 加载仓库数据
		async loadWarehouses() {
			try {
				const response = await getWarehouseListApi({ pageNo: 1, pageSize: 100 })
				let warehouses = []
				if (response && response.data) {
					warehouses = handleTree(response.data, 'id', 'parentId')
				}

				this.warehouseTreeData = warehouses
			} catch (error) {
				this.warehouseTreeData = []
			}
		},

		// 加载仓位数据
		async loadLocations(warehouseId) {
			try {
				const params = { pageNo: 1, pageSize: 100 }
				if (warehouseId) {
					params.warehouseId = warehouseId
				}

				const response = await getWarehouseLocationPageApi(params)
				let locations = []
				if (response && response.data && response.data.list) {
					locations = response.data.list
				} else if (response && response.data && Array.isArray(response.data)) {
					locations = response.data
				} else if (Array.isArray(response)) {
					locations = response
				}

				this.locationOptions = locations
			} catch (error) {
				this.locationOptions = []
			}
		},

		// 仓库变化处理
		handleWarehouseChange(e) {
			const warehouseId = e.detail ? e.detail.value : e
			// 清空仓位选择
			this.formData.locationId = null
			// 加载对应仓库的仓位
			if (warehouseId) {
				this.loadLocations(warehouseId)
			} else {
				this.locationOptions = []
			}
		},

		// 加载数据
		async loadData() {
			if (!this.stockId) {
				return
			}

			this.loading = true
			try {

				// 加载库存信息
				const stockInfoResponse = await stockInfoApi.getStockInfo(this.stockId)


				// 处理API响应数据结构
				let stockInfo = null
				if (stockInfoResponse && stockInfoResponse.data) {
					stockInfo = stockInfoResponse.data
				} else if (stockInfoResponse && typeof stockInfoResponse === 'object') {
					stockInfo = stockInfoResponse
				}

				if (!stockInfo) {
					throw new Error('库存信息不存在或数据格式错误')
				}

				this.formData = stockInfo

				// 如果有仓库ID，加载对应的仓位数据
				if (stockInfo.warehouseId) {
					await this.loadLocations(stockInfo.warehouseId)
				}

				// 加载交易明细
				try {
					const transactionResponse = await stockInfoApi.getTransactionListByInventoryId(this.stockId)

					let transactions = []
					if (transactionResponse && transactionResponse.data) {
						transactions = transactionResponse.data
					} else if (Array.isArray(transactionResponse)) {
						transactions = transactionResponse
					}

					this.transactionList = transactions
				} catch (transactionError) {
					this.transactionList = []
				}

			} catch (error) {
				this.formData = null
				uni.showToast({
					title: error.message || '加载数据失败',
					icon: 'error',
					duration: 2000
				})

				// 如果是数据不存在，延迟返回上一页
				if (error.message === '库存信息不存在') {
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
				}
			} finally {
				this.loading = false
			}
		},

		// 保存数据
		async handleSave() {
			if (!this.formData) return

			try {
				// 表单验证
				await this.$refs.formRef.validate()

				this.saving = true

				await stockInfoApi.updateStockInfo(JSON.stringify(this.formData))

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)

			} catch (error) {
				console.error('保存失败:', error)
				if (error.errorFields) {
					// 表单验证失败
					uni.showToast({
						title: '请检查表单输入',
						icon: 'error'
					})
				} else {
					// 接口调用失败
					uni.showToast({
						title: '保存失败',
						icon: 'error'
					})
				}
			} finally {
				this.saving = false
			}
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0'
			if (isNaN(value)) return '0'
			return Number(value).toLocaleString()
		},

		// 格式化金额
		formatAmount(value) {
			if (!value && value !== 0) return '¥0.00'
			return '¥' + Number(value).toFixed(2)
		},

		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '-'
			const date = new Date(timestamp)
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
		},

		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId && unitId !== 0) return ''

			// 确保unitOptions存在且为数组
			if (!Array.isArray(this.unitOptions)) {
				return unitId.toString()
			}

			// 处理数字类型
			if (typeof unitId === 'number') {
				const unit = this.unitOptions.find(unit => unit.id === unitId)
				if (unit && unit.name) return unit.name
			}

			// 处理字符串类型
			if (typeof unitId === 'string') {
				// 先尝试按字符串查找
				let unit = this.unitOptions.find(unit => unit.id && unit.id.toString() === unitId)
				if (unit && unit.name) return unit.name

				// 再尝试转换为数字查找
				const numId = parseInt(unitId)
				if (!isNaN(numId)) {
					unit = this.unitOptions.find(unit => unit.id === numId)
					if (unit && unit.name) return unit.name
				}
			}

			return unitId ? unitId.toString() : ''
		},

		// 获取物料类型文本
		getMaterialTypeText(type) {
			if (!type) return '-'
			const option = this.materialTypeOptions.find(item => item.value === type)
			return option ? option.label : type
		},

		// 获取物料来源文本
		getMaterialSourceText(source) {
			if (!source) return '-'
			const option = this.materialSourceOptions.find(item => item.value === source)
			return option ? option.label : source
		},

		// 获取状态文本
		getStatusText(status) {
			if (!status) return '-'
			const option = this.statusOptions.find(item => item.value === status)
			return option ? option.label : status
		},

		// 获取交易类型文本
		getTransactionTypeText(type) {
			if (!type) return '-'
			const options = this.dictData[DICT_TYPE.SCM_BIZ_TYPE] || []
			const option = options.find(item => item.value === type)
			return option ? option.label : type
		},

		// 获取交易方向文本
		getTransactionDirectionText(direction) {
			if (!direction) return '-'
			const options = this.dictData[DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION] || []
			const option = options.find(item => item.value === direction)
			return option ? option.label : direction
		},

	}
}
</script>

<style lang="scss" scoped>
.edit-container {
	height: 100vh;
	width: 100vw;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
}

.form-content {
	flex: 1;
	padding: 32rpx 40rpx;
	padding-bottom: 140rpx; // 为底部按钮留出空间
}

.transaction-section {
	margin-top: 60rpx;

	.transaction-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 32rpx;
		padding-bottom: 16rpx;
		border-bottom: 2px solid #f0f0f0;
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	padding: 32rpx 40rpx;
	border-top: 1px solid #eee;
	z-index: 100;

	::deep .uv-button {
		border-radius: 16rpx !important;
		height: 88rpx !important;
		font-size: 32rpx !important;
		font-weight: 600 !important;
	}
}

.empty-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 60vh;

	.empty-content {
		text-align: center;

		.empty-text {
			font-size: 32rpx;
			color: #999;
			margin-bottom: 40rpx;
			display: block;
		}
	}
}

.transaction-list {
	.transaction-item {
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		border: 1px solid #e9ecef;

		&:last-child {
			margin-bottom: 0;
		}

		.transaction-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			padding-bottom: 16rpx;
			border-bottom: 1px solid #dee2e6;

			.transaction-type {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
			}

			.transaction-date {
				font-size: 24rpx;
				color: #666;
			}
		}

		.transaction-details {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.transaction-no {
				font-size: 26rpx;
				color: #495057;
			}

			.transaction-batch {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-quantities {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.transaction-quantity {
				font-size: 26rpx;
				color: #495057;
			}

			.lock-quantity {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-before-after {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.before-quantity {
				font-size: 26rpx;
				color: #495057;
			}

			.after-quantity {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-warehouse {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;
			padding: 8rpx 0;

			.from-warehouse {
				font-size: 26rpx;
				color: #495057;
			}

			.to-warehouse {
				font-size: 26rpx;
				color: #495057;
			}
		}

		.transaction-remark {
			margin-top: 16rpx;
			padding-top: 16rpx;
			border-top: 1px solid #dee2e6;

			.remark-text {
				font-size: 26rpx;
				color: #495057;
				line-height: 1.5;
			}
		}
	}
}

.no-data {
	text-align: center;
	padding: 80rpx 0;
	color: #6c757d;
	font-size: 28rpx;
}

.loading-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
</style>
